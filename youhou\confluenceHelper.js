// ==UserScript==
// @name         Confluence内容插入助手
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  在Confluence查看态页面插入内容，通过个人访问令牌调用API
// <AUTHOR> Tse
// @match        https://wiki.firstshare.cn/pages/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @grant        GM_addStyle
// ==/UserScript==

(function() {
    'use strict';

    // 配置常量
    const CONFIG = {
        STORAGE_KEY: 'confluence_helper_config',
        API_ENDPOINTS: {
            CONTENT: '/rest/api/content',
            SPACE: '/rest/api/space'
        }
    };

    // 全局变量
    let confluenceConfig = {
        baseUrl: '',
        apiToken: '',
        spaceKey: '',
        pageId: ''
    };

    // 初始化
    function init() {
        loadConfig();
        detectPageInfo();
        createUI();
        addStyles();
    }

    // 加载配置
    function loadConfig() {
        const savedConfig = GM_getValue(CONFIG.STORAGE_KEY, '{}');
        confluenceConfig = { ...confluenceConfig, ...JSON.parse(savedConfig) };
    }

    // 保存配置
    function saveConfig() {
        GM_setValue(CONFIG.STORAGE_KEY, JSON.stringify(confluenceConfig));
    }

    // 检测页面信息
    function detectPageInfo() {
        const url = window.location.href;

        // 提取base URL
        const urlParts = url.split('/wiki/');
        if (urlParts.length > 1) {
            confluenceConfig.baseUrl = urlParts[0];
        }

        // 提取页面ID
        const pageIdMatch = url.match(/pages\/(\d+)/);
        if (pageIdMatch) {
            confluenceConfig.pageId = pageIdMatch[1];
        }

        // 尝试从页面元素获取空间键
        const spaceKeyElement = document.querySelector('[data-space-key]');
        if (spaceKeyElement) {
            confluenceConfig.spaceKey = spaceKeyElement.getAttribute('data-space-key');
        }
    }

    // 添加样式
    function addStyles() {
        GM_addStyle(`
            .confluence-helper-panel {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 350px;
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                display: none;
            }

            .confluence-helper-header {
                background: #0052cc;
                color: white;
                padding: 12px 16px;
                border-radius: 8px 8px 0 0;
                font-weight: 600;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .confluence-helper-close {
                background: none;
                border: none;
                color: white;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .confluence-helper-body {
                padding: 16px;
                max-height: 500px;
                overflow-y: auto;
            }

            .confluence-helper-field {
                margin-bottom: 12px;
            }

            .confluence-helper-label {
                display: block;
                margin-bottom: 4px;
                font-weight: 500;
                color: #333;
                font-size: 14px;
            }

            .confluence-helper-input,
            .confluence-helper-textarea {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                box-sizing: border-box;
            }

            .confluence-helper-textarea {
                min-height: 100px;
                resize: vertical;
                font-family: monospace;
            }

            .confluence-helper-button {
                background: #0052cc;
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                margin-right: 8px;
                margin-bottom: 8px;
            }

            .confluence-helper-button:hover {
                background: #0747a6;
            }

            .confluence-helper-button.secondary {
                background: #f4f5f7;
                color: #333;
                border: 1px solid #ddd;
            }

            .confluence-helper-button.secondary:hover {
                background: #e4e6ea;
            }

            .confluence-helper-toggle {
                position: fixed;
                top: 20px;
                right: 20px;
                background: #0052cc;
                color: white;
                border: none;
                padding: 12px 16px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                z-index: 9999;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            }

            .confluence-helper-toggle:hover {
                background: #0747a6;
            }

            .confluence-helper-status {
                padding: 8px 12px;
                border-radius: 4px;
                margin-bottom: 12px;
                font-size: 14px;
            }

            .confluence-helper-status.success {
                background: #e3fcef;
                color: #006644;
                border: 1px solid #79e2a0;
            }

            .confluence-helper-status.error {
                background: #ffebe6;
                color: #bf2600;
                border: 1px solid #ff8f73;
            }

            .confluence-helper-tabs {
                display: flex;
                border-bottom: 1px solid #ddd;
                margin-bottom: 16px;
            }

            .confluence-helper-tab {
                padding: 8px 16px;
                background: none;
                border: none;
                cursor: pointer;
                font-size: 14px;
                color: #666;
                border-bottom: 2px solid transparent;
            }

            .confluence-helper-tab.active {
                color: #0052cc;
                border-bottom-color: #0052cc;
            }

            .confluence-helper-tab-content {
                display: none;
            }

            .confluence-helper-tab-content.active {
                display: block;
            }
        `);
    }

    // 创建UI界面
    function createUI() {
        // 创建切换按钮
        const toggleButton = document.createElement('button');
        toggleButton.className = 'confluence-helper-toggle';
        toggleButton.textContent = 'Confluence助手';
        toggleButton.onclick = togglePanel;
        document.body.appendChild(toggleButton);

        // 创建主面板
        const panel = document.createElement('div');
        panel.className = 'confluence-helper-panel';
        panel.id = 'confluence-helper-panel';

        panel.innerHTML = `
            <div class="confluence-helper-header">
                <span>Confluence内容插入助手</span>
                <button class="confluence-helper-close" onclick="togglePanel()">×</button>
            </div>
            <div class="confluence-helper-body">
                <div class="confluence-helper-tabs">
                    <button class="confluence-helper-tab active" onclick="switchTab('config')">配置</button>
                    <button class="confluence-helper-tab" onclick="switchTab('insert')">插入内容</button>
                    <button class="confluence-helper-tab" onclick="switchTab('templates')">模板</button>
                </div>

                <div id="status-message"></div>

                <!-- 配置标签页 -->
                <div id="config-tab" class="confluence-helper-tab-content active">
                    <div class="confluence-helper-field">
                        <label class="confluence-helper-label">Confluence基础URL:</label>
                        <input type="text" id="base-url" class="confluence-helper-input"
                               placeholder="https://wiki.firstshare.cn"
                               value="${confluenceConfig.baseUrl}">
                    </div>
                    <div class="confluence-helper-field">
                        <label class="confluence-helper-label">API令牌:</label>
                        <input type="password" id="api-token" class="confluence-helper-input"
                               placeholder="输入您的Bearer Token"
                               value="${confluenceConfig.apiToken}">
                    </div>
                    <div class="confluence-helper-field">
                        <label class="confluence-helper-label">空间键 (自动检测):</label>
                        <input type="text" id="space-key" class="confluence-helper-input"
                               placeholder="SPACE"
                               value="${confluenceConfig.spaceKey}">
                    </div>
                    <div class="confluence-helper-field">
                        <label class="confluence-helper-label">页面ID (自动检测):</label>
                        <input type="text" id="page-id" class="confluence-helper-input"
                               placeholder="123456"
                               value="${confluenceConfig.pageId}" readonly>
                    </div>
                    <button class="confluence-helper-button" onclick="saveConfiguration()">保存配置</button>
                    <button class="confluence-helper-button secondary" onclick="testConnection()">测试连接</button>
                </div>

                <!-- 插入内容标签页 -->
                <div id="insert-tab" class="confluence-helper-tab-content">
                    <div class="confluence-helper-field">
                        <label class="confluence-helper-label">插入位置:</label>
                        <select id="insert-position" class="confluence-helper-input">
                            <option value="append">页面末尾</option>
                            <option value="prepend">页面开头</option>
                            <option value="replace">替换全部内容</option>
                        </select>
                    </div>
                    <div class="confluence-helper-field">
                        <label class="confluence-helper-label">内容格式:</label>
                        <select id="content-format" class="confluence-helper-input">
                            <option value="storage">Confluence存储格式 (推荐)</option>
                            <option value="wiki">Wiki标记</option>
                            <option value="plain">纯文本</option>
                        </select>
                    </div>
                    <div class="confluence-helper-field">
                        <label class="confluence-helper-label">要插入的内容:</label>
                        <textarea id="content-input" class="confluence-helper-textarea"
                                  placeholder="输入要插入的内容..."></textarea>
                    </div>
                    <button class="confluence-helper-button" onclick="insertContent()">插入内容</button>
                    <button class="confluence-helper-button secondary" onclick="previewContent()">预览</button>
                </div>

                <!-- 模板标签页 -->
                <div id="templates-tab" class="confluence-helper-tab-content">
                    <div class="confluence-helper-field">
                        <label class="confluence-helper-label">选择模板:</label>
                        <select id="template-select" class="confluence-helper-input" onchange="loadTemplate()">
                            <option value="">选择一个模板...</option>
                            <option value="meeting-notes">会议记录</option>
                            <option value="task-list">任务清单</option>
                            <option value="code-block">代码块</option>
                            <option value="info-panel">信息面板</option>
                            <option value="table">表格</option>
                        </select>
                    </div>
                    <div class="confluence-helper-field">
                        <label class="confluence-helper-label">模板内容:</label>
                        <textarea id="template-content" class="confluence-helper-textarea" readonly></textarea>
                    </div>
                    <button class="confluence-helper-button" onclick="useTemplate()">使用此模板</button>
                    <button class="confluence-helper-button secondary" onclick="saveAsTemplate()">保存为模板</button>
                </div>
            </div>
        `;

        document.body.appendChild(panel);

        // 绑定全局函数
        window.togglePanel = togglePanel;
        window.switchTab = switchTab;
        window.saveConfiguration = saveConfiguration;
        window.testConnection = testConnection;
        window.insertContent = insertContent;
        window.previewContent = previewContent;
        window.loadTemplate = loadTemplate;
        window.useTemplate = useTemplate;
        window.saveAsTemplate = saveAsTemplate;
    }

    // 切换面板显示/隐藏
    function togglePanel() {
        const panel = document.getElementById('confluence-helper-panel');
        const toggleButton = document.querySelector('.confluence-helper-toggle');

        if (panel.style.display === 'none' || panel.style.display === '') {
            panel.style.display = 'block';
            toggleButton.style.display = 'none';
        } else {
            panel.style.display = 'none';
            toggleButton.style.display = 'block';
        }
    }

    // 切换标签页
    function switchTab(tabName) {
        // 隐藏所有标签页内容
        document.querySelectorAll('.confluence-helper-tab-content').forEach(tab => {
            tab.classList.remove('active');
        });

        // 移除所有标签按钮的激活状态
        document.querySelectorAll('.confluence-helper-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // 显示选中的标签页
        document.getElementById(tabName + '-tab').classList.add('active');

        // 激活对应的标签按钮
        document.querySelectorAll('.confluence-helper-tab').forEach(tab => {
            if (tab.textContent.includes(getTabDisplayName(tabName))) {
                tab.classList.add('active');
            }
        });
    }

    // 获取标签页显示名称
    function getTabDisplayName(tabName) {
        const names = {
            'config': '配置',
            'insert': '插入内容',
            'templates': '模板'
        };
        return names[tabName] || tabName;
    }

    // 显示状态消息
    function showStatus(message, type = 'success') {
        const statusDiv = document.getElementById('status-message');
        statusDiv.className = `confluence-helper-status ${type}`;
        statusDiv.textContent = message;
        statusDiv.style.display = 'block';

        // 3秒后自动隐藏
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }

    // 保存配置
    function saveConfiguration() {
        confluenceConfig.baseUrl = document.getElementById('base-url').value.trim();
        confluenceConfig.apiToken = document.getElementById('api-token').value.trim();
        confluenceConfig.spaceKey = document.getElementById('space-key').value.trim();
        confluenceConfig.pageId = document.getElementById('page-id').value.trim();

        saveConfig();
        showStatus('配置已保存！');
    }

    // 测试连接
    function testConnection() {
        if (!confluenceConfig.baseUrl || !confluenceConfig.apiToken) {
            showStatus('请先填写完整的配置信息', 'error');
            return;
        }

        GM_xmlhttpRequest({
            method: 'GET',
            url: confluenceConfig.baseUrl + '/rest/api/user/current',
            headers: {
                'Authorization': 'Bearer ' + confluenceConfig.apiToken,
                'Accept': 'application/json'
            },
            onload: function(response) {
                if (response.status === 200) {
                    const user = JSON.parse(response.responseText);
                    showStatus(`连接成功！当前用户: ${user.displayName || user.username || '未知用户'}`);
                } else {
                    showStatus('连接失败，请检查配置信息', 'error');
                }
            },
            onerror: function() {
                showStatus('连接失败，请检查网络和配置', 'error');
            }
        });
    }

    // 获取页面当前内容
    function getCurrentPageContent() {
        return new Promise((resolve, reject) => {
            if (!confluenceConfig.pageId) {
                reject('页面ID未找到');
                return;
            }

            GM_xmlhttpRequest({
                method: 'GET',
                url: `${confluenceConfig.baseUrl}/rest/api/content/${confluenceConfig.pageId}?expand=body.storage,version`,
                headers: {
                    'Authorization': 'Bearer ' + confluenceConfig.apiToken,
                    'Accept': 'application/json'
                },
                onload: function(response) {
                    if (response.status === 200) {
                        resolve(JSON.parse(response.responseText));
                    } else {
                        reject('获取页面内容失败: ' + response.status);
                    }
                },
                onerror: function() {
                    reject('网络请求失败');
                }
            });
        });
    }

    // 插入内容
    function insertContent() {
        const content = document.getElementById('content-input').value.trim();
        const position = document.getElementById('insert-position').value;
        const format = document.getElementById('content-format').value;

        if (!content) {
            showStatus('请输入要插入的内容', 'error');
            return;
        }

        if (!confluenceConfig.baseUrl || !confluenceConfig.apiToken || !confluenceConfig.pageId) {
            showStatus('请先完成配置并确保页面ID正确', 'error');
            return;
        }

        showStatus('正在插入内容...', 'info');

        getCurrentPageContent().then(pageData => {
            let newContent = '';
            const currentContent = pageData.body.storage.value;

            // 根据格式转换内容
            let formattedContent = formatContent(content, format);

            // 根据位置插入内容
            switch (position) {
                case 'prepend':
                    newContent = formattedContent + '\n' + currentContent;
                    break;
                case 'append':
                    newContent = currentContent + '\n' + formattedContent;
                    break;
                case 'replace':
                    newContent = formattedContent;
                    break;
                default:
                    newContent = currentContent + '\n' + formattedContent;
            }

            // 更新页面
            updatePageContent(pageData, newContent);
        }).catch(error => {
            showStatus('插入失败: ' + error, 'error');
        });
    }

    // 格式化内容
    function formatContent(content, format) {
        switch (format) {
            case 'storage':
                return content; // 假设用户已经提供了正确的存储格式
            case 'wiki':
                return convertWikiToStorage(content);
            case 'plain':
                return `<p>${content.replace(/\n/g, '</p><p>')}</p>`;
            default:
                return content;
        }
    }

    // 简单的Wiki到存储格式转换
    function convertWikiToStorage(wikiContent) {
        return wikiContent
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/^# (.*$)/gm, '<h1>$1</h1>')
            .replace(/^## (.*$)/gm, '<h2>$1</h2>')
            .replace(/^### (.*$)/gm, '<h3>$1</h3>')
            .replace(/\n/g, '</p><p>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');
    }

    // 更新页面内容
    function updatePageContent(pageData, newContent) {
        const updateData = {
            id: pageData.id,
            type: pageData.type,
            title: pageData.title,
            space: pageData.space,
            body: {
                storage: {
                    value: newContent,
                    representation: 'storage'
                }
            },
            version: {
                number: pageData.version.number + 1
            }
        };

        GM_xmlhttpRequest({
            method: 'PUT',
            url: `${confluenceConfig.baseUrl}/rest/api/content/${pageData.id}`,
            headers: {
                'Authorization': 'Bearer ' + confluenceConfig.apiToken,
                'Content-Type': 'application/json'
            },
            data: JSON.stringify(updateData),
            onload: function(response) {
                if (response.status === 200) {
                    showStatus('内容插入成功！页面将自动刷新...');
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    showStatus('插入失败: ' + response.status, 'error');
                }
            },
            onerror: function() {
                showStatus('网络请求失败', 'error');
            }
        });
    }

    // 预览内容
    function previewContent() {
        const content = document.getElementById('content-input').value.trim();
        const format = document.getElementById('content-format').value;

        if (!content) {
            showStatus('请输入要预览的内容', 'error');
            return;
        }

        const formattedContent = formatContent(content, format);

        // 创建预览窗口
        const previewWindow = window.open('', '_blank', 'width=800,height=600');
        previewWindow.document.write(`
            <html>
                <head>
                    <title>内容预览</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; }
                        h1, h2, h3 { color: #333; }
                        p { line-height: 1.6; }
                    </style>
                </head>
                <body>
                    <h1>内容预览</h1>
                    <hr>
                    ${formattedContent}
                </body>
            </html>
        `);
        previewWindow.document.close();
    }

    // 模板定义
    const templates = {
        'meeting-notes': `<h2>会议记录</h2>
<p><strong>会议时间：</strong> ${new Date().toLocaleDateString()}</p>
<p><strong>参会人员：</strong> </p>
<p><strong>会议主题：</strong> </p>
<h3>讨论要点</h3>
<ul>
<li>要点1</li>
<li>要点2</li>
<li>要点3</li>
</ul>
<h3>行动项</h3>
<table>
<tr><th>任务</th><th>负责人</th><th>截止时间</th></tr>
<tr><td></td><td></td><td></td></tr>
</table>`,

        'task-list': `<h2>任务清单</h2>
<p><strong>创建时间：</strong> ${new Date().toLocaleDateString()}</p>
<ul>
<li>☐ 任务1</li>
<li>☐ 任务2</li>
<li>☐ 任务3</li>
</ul>`,

        'code-block': `<h3>代码示例</h3>
<ac:structured-macro ac:name="code">
<ac:parameter ac:name="language">javascript</ac:parameter>
<ac:plain-text-body><![CDATA[
// 在此处添加您的代码
function example() {
    console.log('Hello, World!');
}
]]></ac:plain-text-body>
</ac:structured-macro>`,

        'info-panel': `<ac:structured-macro ac:name="info">
<ac:rich-text-body>
<p>这是一个信息面板，用于显示重要信息。</p>
</ac:rich-text-body>
</ac:structured-macro>`,

        'table': `<table>
<tr>
<th>列1</th>
<th>列2</th>
<th>列3</th>
</tr>
<tr>
<td>数据1</td>
<td>数据2</td>
<td>数据3</td>
</tr>
<tr>
<td>数据4</td>
<td>数据5</td>
<td>数据6</td>
</tr>
</table>`
    };

    // 加载模板
    function loadTemplate() {
        const templateSelect = document.getElementById('template-select');
        const templateContent = document.getElementById('template-content');
        const selectedTemplate = templateSelect.value;

        if (selectedTemplate && templates[selectedTemplate]) {
            templateContent.value = templates[selectedTemplate];
        } else {
            templateContent.value = '';
        }
    }

    // 使用模板
    function useTemplate() {
        const templateContent = document.getElementById('template-content').value;
        if (templateContent) {
            document.getElementById('content-input').value = templateContent;
            switchTab('insert');
            showStatus('模板已加载到插入内容区域');
        } else {
            showStatus('请先选择一个模板', 'error');
        }
    }

    // 保存为模板
    function saveAsTemplate() {
        const content = document.getElementById('content-input').value.trim();
        if (!content) {
            showStatus('请先在插入内容区域输入内容', 'error');
            return;
        }

        const templateName = prompt('请输入模板名称:');
        if (templateName) {
            // 这里可以扩展为保存到本地存储
            showStatus(`模板 "${templateName}" 已保存（功能待完善）`);
        }
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();