# Confluence内容插入助手 - 使用说明

## 🔧 配置说明

### 认证方式
本脚本使用Bearer Token认证，无需邮箱地址。

### 配置项说明
- **基础URL**: 您的Confluence实例地址，如 `https://wiki.firstshare.cn`
- **API令牌**: 您的个人访问令牌（Bearer Token）
- **页面ID**: 自动从当前页面URL提取

## 🚀 快速开始

### 1. 安装脚本
将脚本安装到Tampermonkey中，支持以下URL模式：
- `https://wiki.firstshare.cn/pages/*`

### 2. 获取API令牌
根据您的Confluence系统获取个人访问令牌。

### 3. 使用精简版（推荐）
1. 在Confluence页面点击"快速插入"按钮
2. 填写基础URL和API令牌
3. 输入要插入的HTML内容
4. 点击"保存并插入"

### 4. 使用完整版
1. 在Confluence页面点击"Confluence助手"按钮
2. 在配置标签页填写信息
3. 测试连接确保配置正确
4. 在插入内容标签页操作

## 📝 内容格式示例

### 基本HTML格式
```html
<p>这是一个段落</p>
<h2>这是标题</h2>
<ul>
<li>列表项1</li>
<li>列表项2</li>
</ul>
```

### Confluence宏格式
```html
<ac:structured-macro ac:name="info">
<ac:rich-text-body>
<p>这是信息面板</p>
</ac:rich-text-body>
</ac:structured-macro>
```

### 代码块格式
```html
<ac:structured-macro ac:name="code">
<ac:parameter ac:name="language">javascript</ac:parameter>
<ac:plain-text-body><![CDATA[
console.log('Hello World');
]]></ac:plain-text-body>
</ac:structured-macro>
```

## ⚠️ 注意事项

1. **权限要求**: 确保对目标页面有编辑权限
2. **令牌安全**: API令牌具有高权限，请妥善保管
3. **内容备份**: 重要操作前建议先备份
4. **格式检查**: 使用预览功能检查内容格式

## 🔍 故障排除

### 连接失败
- 检查基础URL格式是否正确
- 确认API令牌有效
- 检查网络连接

### 插入失败
- 确认页面编辑权限
- 检查页面ID是否正确
- 验证内容格式

### 页面ID问题
页面ID会从URL自动提取，格式如：
`https://wiki.firstshare.cn/pages/123456/page-title`
其中 `123456` 就是页面ID。

## 📊 API限制

- 请避免频繁调用API
- 大量内容建议分批插入
- 遵守系统的使用限制

## 🆘 技术支持

如遇问题，请检查：
1. 浏览器控制台错误信息
2. Tampermonkey脚本状态
3. Confluence系统版本兼容性
4. 网络连接状态

---

**版本**: v1.0  
**适用于**: Confluence系统  
**认证方式**: Bearer Token
