// ==UserScript==
// @name         Confluence内容插入助手 (精简版)
// @namespace    http://tampermonkey.net/
// @version      1.0-lite
// @description  Confluence页面内容插入工具 - 精简版
// <AUTHOR> Tse
// @match        https://wiki.firstshare.cn/pages/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @grant        GM_addStyle
// ==/UserScript==

(function() {
    'use strict';

    // 简化的配置
    let config = {
        baseUrl: '',
        apiToken: '',
        pageId: ''
    };

    // 初始化
    function init() {
        loadConfig();
        detectPageInfo();
        createButton();
        addStyles();
    }

    // 加载配置
    function loadConfig() {
        const saved = GM_getValue('confluence_lite_config', '{}');
        config = { ...config, ...JSON.parse(saved) };
    }

    // 保存配置
    function saveConfig() {
        GM_setValue('confluence_lite_config', JSON.stringify(config));
    }

    // 检测页面信息
    function detectPageInfo() {
        const url = window.location.href;
        const urlParts = url.split('/wiki/');
        if (urlParts.length > 1) {
            config.baseUrl = urlParts[0];
        }
        
        const pageIdMatch = url.match(/pages\/(\d+)/);
        if (pageIdMatch) {
            config.pageId = pageIdMatch[1];
        }
    }

    // 添加样式
    function addStyles() {
        GM_addStyle(`
            .confluence-lite-btn {
                position: fixed;
                top: 20px;
                right: 20px;
                background: #0052cc;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                cursor: pointer;
                z-index: 9999;
                font-size: 14px;
            }
            .confluence-lite-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10000;
                display: none;
            }
            .confluence-lite-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 20px;
                border-radius: 8px;
                width: 400px;
                max-height: 80vh;
                overflow-y: auto;
            }
            .confluence-lite-input {
                width: 100%;
                padding: 8px;
                margin: 5px 0;
                border: 1px solid #ddd;
                border-radius: 4px;
                box-sizing: border-box;
            }
            .confluence-lite-textarea {
                width: 100%;
                height: 150px;
                padding: 8px;
                margin: 5px 0;
                border: 1px solid #ddd;
                border-radius: 4px;
                box-sizing: border-box;
                resize: vertical;
            }
            .confluence-lite-btn-group {
                margin-top: 15px;
                text-align: right;
            }
            .confluence-lite-btn-small {
                background: #0052cc;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                cursor: pointer;
                margin-left: 5px;
            }
            .confluence-lite-btn-small.secondary {
                background: #6c757d;
            }
        `);
    }

    // 创建按钮
    function createButton() {
        const btn = document.createElement('button');
        btn.className = 'confluence-lite-btn';
        btn.textContent = '快速插入';
        btn.onclick = showModal;
        document.body.appendChild(btn);
    }

    // 显示模态框
    function showModal() {
        const modal = document.createElement('div');
        modal.className = 'confluence-lite-modal';
        modal.innerHTML = `
            <div class="confluence-lite-content">
                <h3>Confluence内容插入</h3>
                
                <label>基础URL:</label>
                <input type="text" id="lite-url" class="confluence-lite-input"
                       value="${config.baseUrl}" placeholder="https://wiki.firstshare.cn">

                <label>API令牌:</label>
                <input type="password" id="lite-token" class="confluence-lite-input"
                       value="${config.apiToken}" placeholder="Bearer Token">
                
                <label>页面ID (当前: ${config.pageId}):</label>
                <input type="text" id="lite-pageid" class="confluence-lite-input" 
                       value="${config.pageId}" placeholder="页面ID">
                
                <label>要插入的内容:</label>
                <textarea id="lite-content" class="confluence-lite-textarea" 
                          placeholder="输入HTML格式的内容..."></textarea>
                
                <div class="confluence-lite-btn-group">
                    <button class="confluence-lite-btn-small secondary" onclick="closeModal()">取消</button>
                    <button class="confluence-lite-btn-small" onclick="saveAndInsert()">保存并插入</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        modal.style.display = 'block';
        
        // 点击背景关闭
        modal.onclick = function(e) {
            if (e.target === modal) {
                closeModal();
            }
        };
        
        // 绑定全局函数
        window.closeModal = function() {
            modal.remove();
        };
        
        window.saveAndInsert = function() {
            // 保存配置
            config.baseUrl = document.getElementById('lite-url').value.trim();
            config.apiToken = document.getElementById('lite-token').value.trim();
            config.pageId = document.getElementById('lite-pageid').value.trim();
            saveConfig();

            // 获取内容
            const content = document.getElementById('lite-content').value.trim();
            if (!content) {
                alert('请输入要插入的内容');
                return;
            }

            if (!config.baseUrl || !config.apiToken || !config.pageId) {
                alert('请填写完整的配置信息');
                return;
            }

            // 执行插入
            insertContent(content);
            closeModal();
        };
    }

    // 插入内容
    function insertContent(newContent) {
        // 先获取当前页面内容
        GM_xmlhttpRequest({
            method: 'GET',
            url: `${config.baseUrl}/rest/api/content/${config.pageId}?expand=body.storage,version`,
            headers: {
                'Authorization': 'Bearer ' + config.apiToken,
                'Accept': 'application/json'
            },
            onload: function(response) {
                if (response.status === 200) {
                    const pageData = JSON.parse(response.responseText);
                    const currentContent = pageData.body.storage.value;
                    const updatedContent = currentContent + '\n' + newContent;
                    
                    // 更新页面
                    updatePage(pageData, updatedContent);
                } else {
                    alert('获取页面内容失败: ' + response.status);
                }
            },
            onerror: function() {
                alert('网络请求失败');
            }
        });
    }

    // 更新页面
    function updatePage(pageData, newContent) {
        const updateData = {
            id: pageData.id,
            type: pageData.type,
            title: pageData.title,
            space: pageData.space,
            body: {
                storage: {
                    value: newContent,
                    representation: 'storage'
                }
            },
            version: {
                number: pageData.version.number + 1
            }
        };

        GM_xmlhttpRequest({
            method: 'PUT',
            url: `${config.baseUrl}/rest/api/content/${pageData.id}`,
            headers: {
                'Authorization': 'Bearer ' + config.apiToken,
                'Content-Type': 'application/json'
            },
            data: JSON.stringify(updateData),
            onload: function(response) {
                if (response.status === 200) {
                    alert('内容插入成功！页面将刷新...');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    alert('插入失败: ' + response.status);
                }
            },
            onerror: function() {
                alert('网络请求失败');
            }
        });
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
