<!-- 悬浮目录容器 -->
<div id="my_floating_toc" style="
    position: fixed;
    top: 100px;
    right: 30px;
    width: 360px;
    max-height: 70vh;
    min-height: 0;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    border: 1px solid #eee;
    z-index: 9999;
    transition: box-shadow .2s;
">
  <!-- 拖拽把手 -->
  <div id="my_drag_handle" style="
      cursor: move;
      padding: 10px 18px;
      background: #fafafd;
      border-bottom: 1px solid #f0f0f0;
      font-weight: bold;
      border-radius: 12px 12px 0 0;
      user-select: none;
      display: flex;
      align-items: center;
      ">
      <span id="my_fold_icon" style="transition: transform .2s;margin-right:7px;display:inline-block;">▼</span>
      目录（双击收起）
  </div>
  <!-- 目录内容区 -->
  <div id="my_toc_content" style="padding:10px;overflow:auto;max-height:55vh;"></div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function(){

    // 克隆目录（假设页面有class="toc-macro"的目录）
    var origToc = document.querySelector('.toc-macro');
    if(origToc) {
        var clonedToc = origToc.cloneNode(true);
        clonedToc.style.margin = '0';
        document.getElementById('my_toc_content').appendChild(clonedToc);
    }

    // 拖拽
    var tocBox = document.getElementById('my_floating_toc');
    var handle = document.getElementById('my_drag_handle');
    var offsetX, offsetY, dragging = false;
    handle.onmousedown = function(e) {
        dragging = true;
        offsetX = e.clientX - tocBox.offsetLeft;
        offsetY = e.clientY - tocBox.offsetTop;
        document.onmousemove = function(e) {
            if (dragging) {
                tocBox.style.left = (e.clientX - offsetX) + 'px';
                tocBox.style.top = (e.clientY - offsetY) + 'px';
                tocBox.style.right = 'auto';
                tocBox.style.bottom = 'auto';
            }
        };
        document.onmouseup = function() {
            dragging = false;
            document.onmousemove = null;
            document.onmouseup = null;
        };
    };

    // 收起/展开
    var content = document.getElementById('my_toc_content');
    var foldIcon = document.getElementById('my_fold_icon');
    var isFolded = false;
    handle.ondblclick = function(){
        isFolded = !isFolded;
        if(isFolded){
            content.style.display = 'none';
            foldIcon.style.transform = "rotate(-90deg)";
            tocBox.style.borderRadius = "12px 12px 0 0";
        }else{
            content.style.display = 'block';
            foldIcon.style.transform = "rotate(0deg)";
            tocBox.style.borderRadius = "12px";
        }
    };

    // 悬停高亮
    tocBox.onmouseenter = function(){
        this.style.boxShadow = "0 8px 32px rgba(0,82,204,0.17)";
    }
    tocBox.onmouseleave = function(){
        this.style.boxShadow = "0 8px 24px rgba(0,0,0,0.15)";
    }
});
</script>